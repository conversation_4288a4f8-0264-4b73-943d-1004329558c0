import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/common/sidebar/layout';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import {
  Award,
  CheckCircle,
  Clock,
  Download,
  Eye,
  FileText,
  XCircle,
} from 'lucide-react';
import { toast } from 'react-toastify';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import Link from 'next/link';
import Image from 'next/image';
import BackButton from '@/assets/backButton.svg';
import { Input } from '@/components/common/input';
import useFetch from '@/hooks/useFetch';
import { handleDownloadDocument } from '@/utils/download';
import { Dialog } from '@radix-ui/react-dialog';
import { DialogTrigger } from '@/components/common/dialog';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import { Checkbox } from '@/components/common/checkbox';
import Loader from '@/components/common/loader';
import { usePut } from '@/hooks/usePut';
import DocumentViewModalWithSidebar from '@/components/document/components/modals/documentViewModalWithSidebar';
import axios from 'axios';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { formatDate } from '@/utils/time';

interface TrainingData {
  record: {
    id: string;
    title: string;
    description: string;
    duration: number;
    passingScore: number;
    training_version: {
      passing_score: number;
      file_path?: string;
      file_extension?: string;
      document: {
        title: string;
        id: string;
      };
    };
    category: {
      id: string;
      name: string;
    };
  };
}

interface TrainingInfo {
  due_date: string | any;
  status: string;
  quiz_attempt: {
    started_at: string;
    completed_at: string;
    score: number;
    status: string;
  };
  current_step: string;
  certificate_path: string;
}

interface Question {
  id: string;
  question_text: string;
  question_type: 'MultipleChoice' | 'Boolean' | 'ShortAnswer' | 'FillInBlank';
  points: number;
  correct_answer: string;
  sequence_number: number;
  options?: string[];
}

interface QuizAnswer {
  questionId: string;
  selectedAnswers: number[];
  textAnswer?: string;
  blankAnswers?: string[];
}

interface StartQuizResponse {
  questions: Question[];
}

const TakeQuiz = () => {
  const router = useRouter();
  const { trainingId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);
  const { postData, isLoading } = usePost();
  const {
    postData: startQuiz,
    isLoading: startQuizLoading,
    response: startQuizResponse,
    error: startQuizError,
  } = usePost<StartQuizResponse>();

  const {
    putData,
    isLoading: updateLoading,
    response: updateResponse,
    error: updateError,
  } = usePut();

  const {
    putData: quizData,
    isLoading: quizUpdateLoading,
    response: quizUpdateResponse,
    error: quizUpdateError,
  } = usePut();

  // ALL STATE HOOKS FIRST
  const [answers, setAnswers] = useState<Record<string, QuizAnswer>>({});
  const [showValidation, setShowValidation] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [preview, setPreview] = useState(false);
  const [documentViewModal, setDocumentViewModal] = useState(false);
  const [documentData, setDocumentData] = useState<any>(null);
  const [questions, setQuestions] = useState<Question[]>([]);

  const [currentStep, setCurrentStep] = useState<
    'instructions' | 'quiz' | 'complete'
  >('instructions');

  // ALL DATA FETCHING HOOKS

  const {
    data: trainingData,
    isLoading: trainingDataLoading,
    error: trainingDataError,
    reFetch: trainingDataReFetch,
  } = useFetch<TrainingData>(
    accessToken as string,
    `trainings/${trainingId}`,
    {},
  );

  const {
    data: trainingInfo,
    isLoading: trainingInfoLoading,
    error: trainingInfoError,
    reFetch: trainingInfoRefetch,
  } = useFetch<TrainingInfo>(
    accessToken as string,
    `trainings/${trainingId}/me`,
    {},
  );

  // Determine the flow based on file_path, document.id and questions
  const hasFilePath = trainingData?.record?.training_version?.file_path;
  const hasDocument = trainingData?.record?.training_version?.document?.id;
  const hasQuestions = questions && questions.length > 0;

  // ALL EFFECT HOOKS
  useEffect(() => {
    if (hasFilePath || hasDocument) {
      // If there's a file or document, always start with instructions
      setCurrentStep('instructions');
    } else if (!hasFilePath && !hasDocument && !startQuizResponse) {
      // If no file/document and haven't called startQuiz yet, call it to check for questions
      proceedToQuiz();
    }
  }, [hasFilePath, hasDocument, hasQuestions]);

  useEffect(() => {
    if (updateResponse) {
      proceedToQuiz();
    }
    if (updateError) {
      toast.error('Failed to proceed to quiz. Please try again.');
    }
  }, [updateResponse, updateError]);

  useEffect(() => {
    if (quizUpdateResponse) {
      toast.success('Quiz submitted successfully!');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
    if (quizUpdateError) {
      toast.error('Failed to submit quiz. Please try again');
    }
  }, [quizUpdateResponse, quizUpdateError]);

  useEffect(() => {
    if (startQuizResponse) {
      // Set questions from startQuizResponse
      if (
        startQuizResponse.questions &&
        startQuizResponse.questions.length > 0
      ) {
        setQuestions(startQuizResponse.questions);
        setCurrentStep('quiz');
      } else {
        // No questions returned, this is a training-only completion
        // Stay on instructions step but show completion option
        setQuestions([]);
      }
    }
    if (startQuizError) {
      toast.error('Failed to start quiz. Please try again.');
    }
  }, [startQuizResponse, startQuizError]);

  // ALL OTHER CONSTANTS AND VARIABLES
  const breadcrumbData = [
    { name: 'People hub', link: '/people' },
    { name: 'My Training', link: '/people/training/my-trainings' },
    {
      name: trainingData?.record?.title ?? '',
      link: '#',
    },
    { name: 'Take Quiz', link: '#' },
  ];

  const questionsList = questions || [];

  const componentLoading = trainingDataLoading || trainingInfoLoading;

  // NOW HANDLE LOADING AND ERROR STATES AFTER ALL HOOKS
  if (componentLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">
            <Loader />
          </div>
        </div>
      </Layout>
    );
  }

  // ALL FUNCTION DEFINITIONS
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleMultipleChoiceAnswer = (
    questionId: string,
    optionIndex: number,
  ) => {
    setAnswers((prev) => {
      return {
        ...prev,
        [questionId]: {
          questionId,
          selectedAnswers: [optionIndex], // Always single selection
        },
      };
    });
  };

  const handleTextAnswer = (questionId: string, text: string) => {
    setAnswers((prev) => {
      const newAnswers = { ...prev };

      if (text.trim().length > 0) {
        // Add or update answer
        newAnswers[questionId] = {
          questionId,
          selectedAnswers: [],
          textAnswer: text,
        };
      } else {
        // Remove answer if text is empty
        delete newAnswers[questionId];
      }

      return newAnswers;
    });
  };

  const handleBlankAnswer = (
    questionId: string,
    blankIndex: number,
    value: string,
  ) => {
    setAnswers((prev) => {
      const newAnswers = { ...prev };

      if (!newAnswers[questionId]) {
        newAnswers[questionId] = {
          questionId,
          selectedAnswers: [],
          blankAnswers: [],
        };
      }

      const blankAnswers = [...(newAnswers[questionId].blankAnswers || [])];
      blankAnswers[blankIndex] = value;

      newAnswers[questionId] = {
        ...newAnswers[questionId],
        blankAnswers,
      };

      return newAnswers;
    });
  };

  const isQuestionAnswered = (question: Question) => {
    const answer = answers[question.id];
    if (!answer) return false;

    if (question.question_type === 'ShortAnswer') {
      return answer.textAnswer && answer.textAnswer.trim().length > 0;
    } else if (question.question_type === 'FillInBlank') {
      const expectedBlanks =
        (question.question_text.match(/\|/g) || []).length + 1;
      return (
        answer.blankAnswers &&
        answer.blankAnswers.length === expectedBlanks &&
        answer.blankAnswers.every((blank) => blank && blank.trim().length > 0)
      );
    } else {
      return answer.selectedAnswers && answer.selectedAnswers.length > 0;
    }
  };

  const areAllQuestionsAnswered = () => {
    return questionsList.every((question) => isQuestionAnswered(question));
  };

  const getAnsweredQuestionsCount = () => {
    return questionsList.filter((question) => isQuestionAnswered(question))
      .length;
  };

  const getUnansweredQuestions = () => {
    return questionsList
      .map((question, index) => ({ question, index }))
      .filter(({ question }) => !isQuestionAnswered(question))
      .map(({ index }) => index + 1);
  };

  const handleProceedToQuiz = async () => {
    if (!isChecked) {
      toast.error('Please confirm that you have read the training content');
      return;
    }
    await putData(
      accessToken || '',
      `trainings/${trainingId}/me/step/complete`,
      { step: 'content' },
    );
  };

  const proceedToQuiz = async () => {
    await startQuiz(
      accessToken || '',
      `trainings/${trainingId}/me/quiz/start`,
      {},
    );
  };

  const handleSubmitTrainingOnly = async () => {
    if (!isChecked) {
      toast.error('Please confirm that you have read the training content');
      return;
    }

    const payload = {
      step: 'content',
    };

    try {
      await putData(
        accessToken || '',
        `trainings/${trainingId}/me/step/complete`,
        { step: 'content' },
      );
      toast.success('Training submitted successfully!');
    } catch (error) {
      toast.error('Failed to submit training. Please try again.');
    }
  };

  const handleSubmit = async () => {
    setShowValidation(true);

    if (!areAllQuestionsAnswered()) {
      const unansweredQuestions = getUnansweredQuestions();
      toast.error(
        `Please answer all questions. Missing: Question ${unansweredQuestions.join(
          ', ',
        )}`,
      );
      return;
    }

    // Transform answers to match the expected API format
    const questionsPayload = questionsList.map((question) => {
      const userAnswer = answers[question.id];
      let answerValue = '';

      if (
        question.question_type === 'MultipleChoice' ||
        question.question_type === 'Boolean'
      ) {
        // For multiple choice and boolean, get the selected option text
        if (
          userAnswer?.selectedAnswers &&
          userAnswer.selectedAnswers.length > 0
        ) {
          const selectedIndex = userAnswer.selectedAnswers[0];
          answerValue = question.options?.[selectedIndex] || '';
        }
      } else if (question.question_type === 'ShortAnswer') {
        // For short answer, use the text answer
        answerValue = userAnswer?.textAnswer || '';
      } else if (question.question_type === 'FillInBlank') {
        // For fill in blanks, join all blank answers with pipe separator
        answerValue = userAnswer?.blankAnswers?.join('|') || '';
      }

      return {
        question_id: question.id,
        answer: answerValue,
      };
    });

    const payload = {
      step: 'quiz',
      questions: questionsPayload,
    };

    console.log('payload', payload);

    try {
      await quizData(
        accessToken as string,
        `trainings/${trainingId}/me/step/complete`,
        payload,
      );
      setQuestions([]);
    } catch (error) {
      toast.error('Failed to submit quiz. Please try again.');
    }
  };

  const handleSaveAndExit = () => {
    // Save current progress
    const payload = {
      trainingId,
      answers: Object.values(answers),
      isComplete: false,
    };

    // Save to localStorage or API
    localStorage.setItem(
      `quiz_progress_${trainingId}`,
      JSON.stringify(payload),
    );
    toast.info('Progress saved');
    router.push(`/people/training/${trainingId}`);
  };

  const handleDownload = (filePath: any, fileName: any) => {
    if (accessToken && filePath) {
      handleDownloadDocument(accessToken, filePath, fileName);
    }
  };

  const handleGetDocument = async (documentId: string) => {
    const orgId =
      typeof window !== 'undefined'
        ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
        : null;

    const NEXT_PUBLIC_URL = process.env.NEXT_PUBLIC_URL;
    const NEXT_PUBLIC_VERSION = process.env.NEXT_PUBLIC_VERSION;

    if (documentId) {
      const baseUrl = NEXT_PUBLIC_URL;
      const productVersion = NEXT_PUBLIC_VERSION;

      const config = {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const url = `${baseUrl}/${productVersion}/documents/${documentId}?source=training`;

      try {
        const response = await axios.get(url, config);
        console.log(response.data.record);
        setDocumentData(response.data.record);
        setDocumentViewModal(true);
        return;
      } catch (error) {
        console.error('Error fetching clauses:', error);
      }
    }
  };

  const renderQuestionWithBlanks = (question: Question) => {
    const parts = question.question_text.split('[...]');
    const expectedBlanks = parts.length - 1;

    return (
      <div className="space-y-3">
        <div className="text-gray-700 leading-relaxed">
          {parts.map((part, index) => (
            <span key={index}>
              {part}
              {index < parts.length - 1 && (
                <span className="inline-block mx-2">
                  <Input
                    value={answers[question.id]?.blankAnswers?.[index] || ''}
                    onChange={(e) =>
                      handleBlankAnswer(question.id, index, e.target.value)
                    }
                    placeholder={`Answer ${index + 1}`}
                    className="inline-block w-32 h-8 text-center border-b-2 border-l-0 border-r-0 border-t-0 rounded-none focus:border-primary-500"
                  />
                </span>
              )}
            </span>
          ))}
        </div>

        {/* Show blank inputs separately for better mobile experience */}
        <div className="md:hidden space-y-2">
          <Label className="text-sm font-medium text-gray-600">
            Your answers:
          </Label>
          {Array.from({ length: expectedBlanks }, (_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 w-16">
                Blank {index + 1}:
              </span>
              <Input
                value={answers[question.id]?.blankAnswers?.[index] || ''}
                onChange={(e) =>
                  handleBlankAnswer(question.id, index, e.target.value)
                }
                placeholder="Your answer"
                className="flex-1"
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />

          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                  href={`/people/training/my-trainings`}
                >
                  <Image src={BackButton} alt="" />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm text-dark-300">Back</div>
              </TooltipContent>
            </Tooltip>
            <div>
              <h1 className="text-dark-300 font-semibold text-[1.55rem] leading-7 flex items-center gap-2.5">
                {trainingData?.record?.title}
              </h1>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Quiz Content - 75% */}
          <div className="xl:col-span-3">
            {trainingInfo?.status === 'Completed' && (
              <div className="bg-green-50 border border-green-200 rounded-lg py-4 px-6 mb-4 flex justify-between">
                <div className="flex items-center">
                  <CheckCircle className="w-8 h-8 text-green-600 mr-4" />
                  <div>
                    <h2 className="text-xl font-semibold text-green-800">
                      Training Completed Successfully!
                    </h2>
                  </div>
                </div>
                <PrimaryButton
                  text="Download Certificate"
                  icon={<Award className="w-6 h-6" />}
                  size="medium"
                  onClick={() => {
                    handleDownload(
                      trainingInfo.certificate_path,
                      `${trainingData?.record?.title}_Certificate.pdf`,
                    );
                  }}
                />
              </div>
            )}

            {/* Flow 1: (file_path OR document.id) exists - show instructions first */}
            {(hasFilePath || hasDocument) && currentStep === 'instructions' && (
              <div className="border border-gray-200 rounded-lg bg-white">
                {/* Header */}
                <div className="py-4 px-6 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-dark-300">
                      Training Instructions
                    </h2>
                  </div>
                </div>

                {/* Instructions */}
                <div className="pt-0 pb-6 px-6">
                  {trainingInfo?.quiz_attempt?.status === 'Failed' && (
                    <div className="bg-red-50 border border-red-200 rounded-lg py-4 px-6 mt-4">
                      <div className="w-full flex items-center justify-between gap-4">
                        <div className="flex items-center flex-nowrap gap-4">
                          <XCircle className="w-8 h-8 text-red-800" />
                          <div>
                            <h2 className="text-xl font-semibold text-red-800">
                              You didn’t pass this time
                            </h2>
                            <p className="text-md text-red-700">
                              Review the training material and try again!
                            </p>
                          </div>
                        </div>
                        <PrimaryButton
                          size="medium"
                          text="Retake Quiz"
                          isLoading={startQuizLoading}
                          onClick={proceedToQuiz}
                        />
                      </div>
                    </div>
                  )}

                  <div className="prose max-w-none">
                    <div className="whitespace-pre-line text-gray-700 leading-relaxed pt-4">
                      {/* {instructions} */}

                      {trainingData?.record?.description}
                    </div>
                  </div>

                  {/* PDF Download Section - only show if file_path exists */}
                  {hasFilePath && (
                    <div className="mt-6 p-6 bg-gray-50 rounded-lg">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                            <FileText className="w-6 h-6 text-primary-500" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-dark-300 mb-2">
                            {trainingData?.record?.title}
                          </h3>

                          {trainingData?.record?.training_version
                            ?.file_path && (
                            <div className="flex space-x-3">
                              {/* <PrimaryButton
                                  text="Download"
                                  icon={<Download className="w-4 h-4" />}
                                  size="medium"
                                  onClick={() => {
                                    const filePath =
                                      trainingData?.record?.training_version
                                        ?.file_path;
                                    handleDownload(
                                      filePath,
                                      filePath?.split('/').pop(),
                                    );
                                  }}
                                /> */}
                              <Dialog open={preview} onOpenChange={setPreview}>
                                <DialogTrigger asChild>
                                  {[
                                    'pdf',
                                    'docx',
                                    'doc',
                                    'jpg',
                                    'png',
                                    'jpeg',
                                  ].includes(
                                    trainingData?.record?.training_version
                                      ?.file_extension as string,
                                  ) && (
                                    <SecondaryButton
                                      text="Launch Module"
                                      icon={<Eye className="w-4 h-4" />}
                                      size="medium"
                                      onClick={() => setPreview(true)}
                                    />
                                  )}
                                </DialogTrigger>
                                {preview && (
                                  <DocumentViewModal
                                    title={
                                      trainingData?.record?.training_version?.file_path
                                        ?.split('/')
                                        .pop() || ''
                                    }
                                    filePath={
                                      trainingData?.record?.training_version
                                        ?.file_path ?? ''
                                    }
                                    extension={
                                      trainingData?.record?.training_version
                                        ?.file_extension as
                                        | 'pdf'
                                        | 'docx'
                                        | 'doc'
                                        | 'png'
                                        | 'jpeg'
                                        | 'jpg'
                                    }
                                    dialogClass="min-w-[95%]"
                                  />
                                )}
                              </Dialog>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Document Section - show if document.id exists but no file_path */}
                  {!hasFilePath && hasDocument && (
                    <div className="mt-6 p-6 bg-gray-50 rounded-lg">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                            <FileText className="w-6 h-6 text-primary-500" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-dark-300 mb-2">
                            {trainingData?.record?.title}
                          </h3>
                          <PrimaryButton
                            text="Launch Module"
                            icon={<Eye className="w-4 h-4" />}
                            size="medium"
                            onClick={() =>
                              handleGetDocument(
                                trainingData?.record?.training_version?.document
                                  ?.id,
                              )
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Consent Section */}

                  {trainingInfo?.status !== 'Completed' && (
                    <div className="pb-4 pt-2 rounded-lg">
                      {trainingInfo?.current_step === 'content' && (
                        <div className="flex items-center justify-end gap-8 mt-4">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="consent"
                              checked={isChecked}
                              onCheckedChange={(checked) =>
                                setIsChecked(checked as boolean)
                              }
                            />
                            <label
                              htmlFor="consent"
                              className="text-base font-normal leading-6 text-dark-300"
                            >
                              I confirm that I have read all the training
                              content and I am ready to take the quiz
                            </label>
                          </div>
                          <div>
                            <PrimaryButton
                              size="medium"
                              text="Proceed to Quiz"
                              disabled={isLoading || !isChecked}
                              isLoading={updateLoading}
                              onClick={handleProceedToQuiz}
                            />
                          </div>
                        </div>
                      )}

                      {trainingInfo?.current_step === 'quiz' &&
                        trainingInfo?.quiz_attempt?.status !== 'Failed' && (
                          <div className="flex items-center justify-end gap-8 mt-4">
                            <div className="flex items-center gap-2">
                              <div>
                                <PrimaryButton
                                  size="medium"
                                  text="Start Quiz"
                                  isLoading={startQuizLoading}
                                  onClick={proceedToQuiz}
                                />
                              </div>
                            </div>
                          </div>
                        )}

                      {/* Show completion option when startQuiz was called but no questions returned */}
                      {startQuizResponse && !hasQuestions && (
                        <div className="flex items-center justify-end gap-8 mt-4">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="training-complete"
                              checked={isChecked}
                              onCheckedChange={(checked) =>
                                setIsChecked(checked as boolean)
                              }
                            />
                            <label
                              htmlFor="training-complete"
                              className="text-base font-normal leading-6 text-dark-300"
                            >
                              I confirm that I have completed the training
                            </label>
                          </div>
                          <div>
                            <PrimaryButton
                              size="medium"
                              text="Submit Completion"
                              disabled={!isChecked}
                              onClick={handleSubmitTrainingOnly}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Show quiz when we have questions and currentStep is quiz */}
            {hasQuestions && currentStep === 'quiz' && (
              <div className="border border-grey-100 rounded-lg bg-white">
                <div className="py-4 px-6 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-dark-300">
                      Questions
                    </h2>
                    <div className="text-sm text-gray-500">
                      {getAnsweredQuestionsCount()} of {questionsList.length}{' '}
                      answered
                    </div>
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  {questionsList.map((question, index) => (
                    <div
                      key={question.id}
                      className={`border rounded-lg p-6 ${
                        showValidation && !isQuestionAnswered(question)
                          ? 'border-red-300 bg-red-50'
                          : isQuestionAnswered(question)
                          ? 'border-green-300 bg-green-50'
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-600 rounded-full font-medium">
                            {index + 1}
                          </span>
                          <h3 className="text-lg font-medium text-dark-300">
                            {question.question_text}
                          </h3>
                        </div>
                        <div className="flex items-center space-x-2">
                          {isQuestionAnswered(question) && (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          )}
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                            {question.question_type
                              .replace(/([A-Z])/g, ' $1')
                              .trim()}
                          </span>
                        </div>
                      </div>

                      {/* Multiple Choice Questions */}
                      {question.question_type === 'MultipleChoice' &&
                        question.options && (
                          <div className="space-y-3">
                            {question.options.map((option, index) => (
                              <label
                                key={index}
                                className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                                  answers[
                                    question.id
                                  ]?.selectedAnswers?.includes(index)
                                    ? 'border-primary-500 bg-primary-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                <input
                                  type="radio"
                                  name={`question-${question.id}`}
                                  checked={
                                    answers[
                                      question.id
                                    ]?.selectedAnswers?.includes(index) || false
                                  }
                                  onChange={() =>
                                    handleMultipleChoiceAnswer(
                                      question.id,
                                      index,
                                    )
                                  }
                                  className="w-4 h-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                                />
                                <span className="text-gray-700 flex-1">
                                  {option}
                                </span>
                              </label>
                            ))}
                          </div>
                        )}

                      {/* Boolean Questions */}
                      {question.question_type === 'Boolean' &&
                        question.options && (
                          <div className="space-y-3">
                            <Label className="text-base font-medium text-dark-100">
                              Select True or False:
                            </Label>
                            <div className="space-y-2">
                              {question.options.map((option, optionIndex) => (
                                <label
                                  key={optionIndex}
                                  className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                                >
                                  <input
                                    type="radio"
                                    name={`question_${question.id}`}
                                    checked={
                                      answers[
                                        question.id
                                      ]?.selectedAnswers?.includes(
                                        optionIndex,
                                      ) || false
                                    }
                                    onChange={() =>
                                      handleMultipleChoiceAnswer(
                                        question.id,
                                        optionIndex,
                                      )
                                    }
                                    className="w-4 h-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                                  />
                                  <span className="text-dark-100">
                                    {option}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        )}

                      {/* Short Answer Questions */}
                      {question.question_type === 'ShortAnswer' && (
                        <div className="space-y-3">
                          <Label className="text-base font-medium text-dark-100">
                            Your Answer:
                          </Label>
                          <Textarea
                            value={answers[question.id]?.textAnswer || ''}
                            onChange={(e) =>
                              handleTextAnswer(question.id, e.target.value)
                            }
                            placeholder="Type your answer here..."
                            rows={4}
                            className="w-full"
                          />
                        </div>
                      )}

                      {/* Fill in Blanks Questions */}
                      {question.question_type === 'FillInBlank' && (
                        <div className="space-y-4">
                          <Label className="text-base font-medium text-dark-100">
                            Fill in the blanks:
                          </Label>
                          <div className="space-y-3">
                            {renderQuestionWithBlanks(question)}
                          </div>
                        </div>
                      )}

                      {showValidation && !isQuestionAnswered(question) && (
                        <p className="text-red-600 text-sm mt-2">
                          This question is required
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Quiz Info Sidebar - 25% */}
          {currentStep === 'quiz' && hasQuestions && (
            <div className="xl:col-span-1">
              <div className="border border-gray-200 rounded-lg bg-white sticky top-6">
                <div className="p-4 border-b border-gray-100">
                  <h3 className="text-lg font-medium text-dark-300">
                    Quiz Information
                  </h3>
                </div>

                <div className="p-4 space-y-4">
                  <div>
                    <Label className="text-base font-medium text-gray-600">
                      Progress
                    </Label>
                    <div className="mt-2">
                      <div className="flex justify-between text-base text-gray-600 mb-1">
                        <span>
                          {getAnsweredQuestionsCount()} of{' '}
                          {questionsList.length}
                        </span>
                        <span>
                          {Math.round(
                            (getAnsweredQuestionsCount() /
                              questionsList.length) *
                              100,
                          )}
                          %
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${
                              (getAnsweredQuestionsCount() /
                                questionsList.length) *
                              100
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-base font-medium text-gray-600">
                      Passing Score
                    </Label>
                    <p className="text-lg font-medium text-dark-300 mt-1">
                      {trainingData?.record?.training_version?.passing_score}%
                    </p>
                  </div>

                  <div>
                    <Label className="text-base font-medium text-gray-600">
                      Total Questions
                    </Label>
                    <p className="text-lg font-medium text-dark-300 mt-1">
                      {questionsList.length}
                    </p>
                  </div>
                </div>

                <div className="p-4 border-t border-gray-100 space-y-3">
                  <PrimaryButton
                    text="Submit"
                    onClick={handleSubmit}
                    isLoading={quizUpdateLoading}
                    disabled={!areAllQuestionsAnswered()}
                    size="medium"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Show training details sidebar when in instructions step */}
          {currentStep === 'instructions' && (
            <div className="xl:col-span-1">
              <div className="border border-gray-200 rounded-lg bg-white sticky top-6">
                <div className="p-4 border-b border-gray-100">
                  <h3 className="text-lg font-medium text-dark-300">
                    Training Details
                  </h3>
                </div>
                <div className="p-4 space-y-4">
                  <div>
                    <Label className="text-base font-medium text-gray-600">
                      Category
                    </Label>
                    <p className="text-dark-300 mt-1">
                      {trainingData?.record?.category?.name}
                    </p>
                  </div>

                  <div>
                    <Label className="text-base font-medium text-gray-600">
                      Due Date
                    </Label>
                    <p className="text-dark-300 mt-1">
                      {formatDate(trainingInfo?.due_date, false)}{' '}
                    </p>
                  </div>
                  <div>
                    <Label className="text-base font-medium text-gray-600">
                      Passing Score
                    </Label>
                    <p className="text-dark-300 mt-1">
                      {trainingData?.record?.training_version?.passing_score}
                    </p>
                  </div>

                  {trainingInfo?.status !== 'Completed' && (
                    <div>
                      <Label className="text-base font-medium text-gray-600">
                        Status
                      </Label>

                      <div className="mt-1 flex items-center">
                        <div className="flex items-center text-gray-500">
                          <Clock className="w-4 h-4 mr-1 text-yellow-500" />
                          <span className="text-base text-yellow-500">
                            {trainingInfo?.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {trainingInfo?.quiz_attempt && (
                <div className="mt-4 border border-gray-200 rounded-lg bg-white sticky top-6">
                  <div className="p-4 border-b border-gray-100">
                    <h3 className="text-lg font-medium text-dark-300">
                      History
                    </h3>
                  </div>
                  <div className="p-4 space-y-4">
                    <div>
                      <Label className="text-base font-medium text-gray-600">
                        Started At
                      </Label>
                      <p className="text-dark-300 mt-1">
                        {formatDate(
                          trainingInfo?.quiz_attempt?.started_at,
                          false,
                        )}
                      </p>
                    </div>

                    <div>
                      <Label className="text-base font-medium text-gray-600">
                        Completed At
                      </Label>
                      <p className="text-dark-300 mt-1">
                        {formatDate(
                          trainingInfo?.quiz_attempt?.completed_at,
                          false,
                        )}
                      </p>
                    </div>

                    <div>
                      <Label className="text-base font-medium text-gray-600">
                        Score
                      </Label>
                      <p className="text-dark-300 mt-1">
                        {trainingInfo?.quiz_attempt?.score}
                      </p>
                    </div>

                    <div>
                      <Label className="text-base font-medium text-gray-600">
                        Outcome
                      </Label>
                      <p
                        className={`${
                          trainingInfo?.quiz_attempt?.status === 'Failed'
                            ? 'text-red-500'
                            : trainingInfo?.quiz_attempt?.status === 'Passed'
                            ? 'text-green-500'
                            : 'text-dark-300'
                        } mt-1`}
                      >
                        {trainingInfo?.quiz_attempt?.status}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <Dialog open={documentViewModal} onOpenChange={setDocumentViewModal}>
        {documentViewModal && (
          <DocumentViewModalWithSidebar
            setDocumentViewModal={setDocumentViewModal}
            extension={documentData?.document_version?.file_extension}
            filePath={documentData?.document_version?.file_path}
            title={documentData?.title}
            version={documentData?.document_version?.version_number}
            publishDate={documentData?.publish_date}
            status={documentData?.status}
            next_review_date={documentData?.next_review_date}
            review_period={documentData?.review_period}
            source={router.asPath.split('?')[0].split('/')[1]}
          />
        )}
      </Dialog>
    </Layout>
  );
};

export default TakeQuiz;
